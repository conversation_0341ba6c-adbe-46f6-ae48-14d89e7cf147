/**
 * @description Logger servisi için temel arayüz.
 * Farklı loglama implementasyonları bu arayüzü uygulamalıdır.
 */
export interface ILoggerService {
  /**
   * @description Bilgilendirme mesajı loglar.
   * @param message Loglanacak mesaj.
   * @param context Opsiyonel ek bağlam verisi.
   */
  info(message: string, context?: unknown): void;

  /**
   * @description Uyarı mesajı loglar.
   * @param message Loglanacak mesaj.
   * @param context Opsiyonel ek bağlam verisi.
   */
  warn(message: string, context?: unknown): void;

  /**
   * @description Hata mesajı loglar.
   * @param message Loglanacak mesaj.
   * @param error Opsiyonel hata nesnesi.
   * @param context Opsiyonel ek bağlam verisi.
   */
  error(message: string, error?: Error, context?: unknown): void;

  /**
   * @description Debug mesajı loglar (genellikle sadece geliştirme ortamında aktif).
   * @param message Loglanacak mesaj.
   * @param context Opsiyonel ek bağlam verisi.
   */
  debug(message: string, context?: unknown): void;
}
