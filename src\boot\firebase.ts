import type { FirebaseOptions } from 'firebase/app';
import type { FirebaseApp } from 'firebase/app';
import { initializeApp } from 'firebase/app';
import type { Firestore } from 'firebase/firestore';
import { getFirestore } from 'firebase/firestore';
import type { Auth } from 'firebase/auth';
import { getAuth } from 'firebase/auth';
import type { FirebaseStorage } from 'firebase/storage';
import { getStorage } from 'firebase/storage';
import { boot } from 'quasar/wrappers';

/**
 * @description Firebase yapılandırması için arayüz.
 */

/**
 * @description Firebase yapılandırması için arayüz. Firebase SDK'sından alınmıştır.
 */
// interface FirebaseConfig extends FirebaseOptions {} // Doğrudan FirebaseOptions kullanılacak

let firebaseApp: FirebaseApp | null = null;
let db: Firestore | null = null;
let auth: Auth | null = null;
let storage: FirebaseStorage | null = null;

/**
 * @description Quasar boot dosyası. Uygulama başlangıcında Firebase'i başlatır.
 */
export default boot(() => {
  // Ortam değişkenini kontrol et
  if (import.meta.env.VITE_DATA_SOURCE_TYPE === 'firebase') {
    const firebaseConfig: FirebaseOptions = {
      apiKey: import.meta.env.VITE_FIREBASE_API_KEY as string,
      authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN as string,
      projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID as string,
      storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET as string,
      messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID as string,
      appId: import.meta.env.VITE_FIREBASE_APP_ID as string,
      ...(import.meta.env.VITE_FIREBASE_MEASUREMENT_ID && {
        measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
      }),
    };

    try {
      // Firebase uygulamasını başlat
      firebaseApp = initializeApp(firebaseConfig);

      // Servisleri al
      db = getFirestore(firebaseApp);
      auth = getAuth(firebaseApp);
      storage = getStorage(firebaseApp);

      console.log('Firebase başarıyla başlatıldı.');

      // Vue uygulamasına servisleri provide etmek isterseniz burada yapabilirsiniz.
      // app.provide('firebaseDb', db);
      // app.provide('firebaseAuth', auth);
      // app.provide('firebaseStorage', storage);
    } catch (error) {
      console.error('Firebase başlatılırken bir hata oluştu:', error);
      // Hata durumunda kullanıcıya bildirim gösterebilirsiniz.
      // import { useQuasar } from 'quasar';
      // const $q = useQuasar();
      // $q.notify({
      //   color: 'negative',
      //   message: 'Firebase başlatılamadı.',
      //   icon: 'report_problem'
      // });
    }
  } else {
    console.log('Veri kaynağı tipi Firebase değil, Firebase başlatılmadı.');
  }
});

/**
 * @description Dışa aktarılan Firebase servisleri.
 */
export { firebaseApp, db, auth, storage };
