# Proje Adı: [Projenizin Adını Buraya Yazın] - Modüler Quasar Uygulaması (Detaylı Plan)

Bu TODO listesi, Quasar Framework ve TypeScript kullanarak SOLID prensiplerine uygun, m<PERSON><PERSON><PERSON>, ç<PERSON><PERSON> dil, ç<PERSON><PERSON> tema, yapılandırılabilir loglama/notifikasyon ve kimlik doğrulama özelliklerine sahip bir web uygulaması geliştirmek için adım adım bir yol haritası sunar.

## 0. Hazırlık ve Ortam Kurulumu

- [x] **Node.js ve Yarn/NPM Kurulumu:** Güncel LTS versiyonlarının kurulu olduğundan emin olun.
- [x] **Quasar CLI Kurulumu:** `yarn global add @quasar/cli` veya `npm install -g @quasar/cli`.
- [x] **IDE Kurulumu ve Eklentiler:**
  - [x] VS Code (önerilir).
  - [x] Volar (Vue 3 için resmi eklenti) + TypeScript Vue Plugin (Volar).
  - [x] ESLint eklentisi.
  - [x] Prettier - Code formatter eklentisi.
  - [x] Quasar Framework (Official) eklentisi (VS Code için).
- [x] **Git Kurulumu ve Proje için Repository Oluşturma:**
  - [x] `git init`
  - [x] Uzak bir repository (GitHub, GitLab vb.) oluşturun ve projeyi bağlayın.
  - [x] `.gitignore` dosyası oluşturun (Quasar CLI genelde bunu yapar).

## 1. Proje Başlatma ve Temel Çevre Yapılandırması

- [x] **Quasar Projesi Oluşturma:**
  - `quasar create <proje-klasor-adi>` komutu çalıştırıldı.
  - Seçimler: Quasar CLI with Vite, TypeScript, Quasar v2 (Vue 3), SCSS, ESLint+Prettier, Pinia, Axios, Vue-i18n.
- [x] **`.env` Ortam Değişkenleri Yönetimi:**
  - [x] **`.env.example` Dosyası Oluşturma ve Yapılandırma:**
    - **Konum:** Proje kök dizini.
    - **İçerik:**
      - `VITE_APP_NAME="Benim Modüler Quasar Uygulamam"`
      - `VITE_APP_VERSION="1.0.0"`
      - `VITE_DATA_SOURCE_TYPE="firebase"` (Olası değerler: "firebase", "restapi", "mock")
      - **REST API Ayarları (Eğer `VITE_DATA_SOURCE_TYPE="restapi"`):**
        - `VITE_API_BASE_URL="http://localhost:8080/api"`
        - `VITE_API_TIMEOUT="30000"`
      - **Firebase Ayarları (Eğer `VITE_DATA_SOURCE_TYPE="firebase"`):**
        - `VITE_FIREBASE_API_KEY="YOUR_FIREBASE_API_KEY_PLACEHOLDER"`
        - `VITE_FIREBASE_AUTH_DOMAIN="YOUR_FIREBASE_AUTH_DOMAIN_PLACEHOLDER"`
        - `VITE_FIREBASE_PROJECT_ID="YOUR_FIREBASE_PROJECT_ID_PLACEHOLDER"`
        - `VITE_FIREBASE_STORAGE_BUCKET="YOUR_FIREBASE_STORAGE_BUCKET_PLACEHOLDER"`
        - `VITE_FIREBASE_MESSAGING_SENDER_ID="YOUR_FIREBASE_MESSAGING_SENDER_ID_PLACEHOLDER"`
        - `VITE_FIREBASE_APP_ID="YOUR_FIREBASE_APP_ID_PLACEHOLDER"`
        - `VITE_FIREBASE_MEASUREMENT_ID="YOUR_FIREBASE_MEASUREMENT_ID_PLACEHOLDER"` (Opsiyonel)
      - **i18n Ayarları:**
        - `VITE_DEFAULT_LOCALE="tr-TR"`
        - `VITE_FALLBACK_LOCALE="en-US"`
      - **Tema Ayarları:**
        - `VITE_DEFAULT_THEME="light"` (Olası değerler: "light", "dark", "auto")
      - **Loglama Ayarları:**
        - `VITE_LOG_LEVEL="debug"` (Olası değerler: "error", "warn", "info", "debug", "trace")
      - **Notifikasyon Ayarları (Quasar Notify):**
        - `VITE_NOTIFICATION_DISPLAY_LEVEL="info"` (Olası değerler: "error", "warn", "info", "success", "none")
        - `VITE_API_ERROR_NOTIFICATION_TYPE="friendly"` (Olası değerler: "toast", "friendly", "silent")
        - `VITE_NOTIFICATION_DEFAULT_POSITION="top-right"` (Quasar Notify pozisyonları)
        - `VITE_NOTIFICATION_DEFAULT_TIMEOUT="5000"` (milisaniye)
        - `VITE_NOTIFICATION_DEFAULT_GROUP="true"` ("true" | "false")
        - `VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL=""` (Boş = Quasar varsayılanı, veya ikon/metin)
    - **Git:** Commit edilecek.
  - [x] **`.gitignore` Dosyasını Gözden Geçirme:**
    - `.env`, `.env.*`, `!.env.example` kurallarının doğruluğundan emin olun.
    - `.env.development`, `.env.production` gibi dosyaların da (hassas bilgi içerebilecekleri ve CI/CD üzerinden yönetilmeleri daha iyi olabileceği için) ignore edilmesi önerilir.
  - [x] **Lokal Geliştirme İçin `.env.local` Dosyası:**
    - `.env.example` dosyasını kopyalayıp proje kök dizininde `.env.local` olarak adlandırın.
    - `.env.local` dosyasına kendi lokal geliştirme ortamınız için gerçek değerleri girin.
    - **Git:** Ignore edilecek.
  - [x] **TypeScript Ortam Değişkeni Tanımları (`src/vite-env.d.ts` veya `src/types/env.d.ts`):**
    - **Sorumluluk:** `import.meta.env` için tip güvenliği sağlamak.
    - **İçerik:** `ImportMetaEnv` arayüzünü `.env.example` dosyasındaki tüm `VITE_` değişkenlerini ve bunların tiplerini (özellikle union type'ları: `VITE_DATA_SOURCE_TYPE: 'firebase' | 'restapi' | 'mock';` vb.) içerecek şekilde tanımlayın.
    - `QNotifyPosition` gibi özel tipleri da burada tanımlayabilirsiniz.
    - **Import Edenler:** Uygulama içinde `import.meta.env` kullanan tüm dosyalar (dolaylı olarak TypeScript derleyicisi tarafından).
- [x] **ESLint ve Prettier Entegrasyonunu Kontrol Etme.**
- [x] **Projenin Çalıştığını Doğrulama (`quasar dev`).**

## 2. Temel Dizin Yapısı ve Modülerlik Anlayışı

- [x] **Ana Dizin Yapısını Oluşturma/Gözden Geçirme (`src/`):**
  - `assets/`
  - `boot/`
  - `components/` (Genel/Paylaşılan Mikro UI Bileşenleri)
  - `composables/` (Genel/Paylaşılan Vue Composition API Fonksiyonları)
  - `constants/` (Uygulama geneli sabitler)
  - `core/`
    - `data-layer/`
      - `implementations/`
        - `firebase/`
        - `rest/`
        - `mock/`
    - `services/`
      - `logger/`
    - `theme/`
    - `types/` (Uygulama geneli TypeScript tanımları)
  - `css/`
  - `i18n/`
    - `en-US/`
    - `tr-TR/`
  - `layouts/`
  - `modules/`
  - `pages/`
  - `router/`
  - `stores/` (Genel/Paylaşılan Pinia Store'ları)
  - `utils/` (Genel yardımcı fonksiyonlar)
- [x] **Modül Yapısı Tanımı (`src/modules/<module-name>/`):**
  - `components/`
  - `composables/`
  - `constants/`
  - `pages/`
  - `router/` (`index.ts` veya `routes.ts` -> `RouteRecordRaw[]` export eder)
  - `services/`
  - `stores/`
  - `types/`
  - `utils/`
  - `i18n/` (`en-US.ts`, `tr-TR.ts` -> çeviri objesi export eder)
  - `index.ts` (Opsiyonel, modülün public API'si için)

## 3. Çekirdek Altyapı ve Servislerin Kurulumu

### 3.1. Logger Servisi

- [x] **`src/core/services/logger/ILogger.ts` Arayüzü Tanımlama:** (error, warn, info, debug, trace metodları)
- [x] **`src/core/services/logger/LoggerService.ts` Implementasyonu:**
  - `ILogger`'ı implemente eder.
  - `import.meta.env.VITE_LOG_LEVEL`'a göre logları filtreler.
  - `console` metodlarına yönlendirir.
- [x] **`src/boot/logger.ts` Boot Dosyası:**
  - `LoggerService` örneğini oluşturur ve Vue app'e `provide` eder veya global hale getirir.
  - `quasar.config.js`'e eklenir.

### 3.2. Quasar Notify Plugin Yapılandırması ve Notifikasyon Servisi

- [x] **Quasar Notify Plugin Kurulum Kontrolü:** `quasar.config.js` -> `framework.plugins: ['Notify']`.
- [x] **`src/boot/notify-defaults.ts` Boot Dosyası:**
  - `import.meta.env` üzerinden `VITE_NOTIFICATION_DEFAULT_...` ayarlarını okur.
  - `Notify.setDefaults({})` ile Quasar Notify için global varsayılanları ayarlar.
  - `quasar.config.js`'e eklenir.
- [x] **`src/core/services/notificationService.ts` (veya `src/composables/useNotify.ts`):**
  - Quasar `Notify`'yi sarmalayan, kullanımı kolaylaştıran fonksiyonlar sunar (`success`, `info`, `warning`, `error`, `apiError`).
  - `VITE_NOTIFICATION_DISPLAY_LEVEL`'a göre filtreleme yapar.
  - `VITE_API_ERROR_NOTIFICATION_TYPE`'a göre API hata bildirimi yapar.
  - Bildirim göstermek isteyen herhangi bir yer tarafından import edilebilir/kullanılabilir.

### 3.3. Çekirdek Veri Katmanı (`Core Data Layer`)

- [x] **`src/core/data-layer/IDataService.ts` Arayüzü Tanımlama:** (CRUD operasyonları, jenerik tipler, okuma/yazma ayrımı)
- [ ] **`src/core/data-layer/implementations/firebase/firebaseDataService.ts`:**
  - `IDataService`'i Firebase (Firestore DB) kullanarak implemente eder.
  - Koşul: `import.meta.env.VITE_DATA_SOURCE_TYPE === 'firebase'`.
  - Import: `IDataService`, Firebase SDK, `firebaseApp` (aşağıdaki boot dosyasından).
- [ ] **`src/boot/firebase.ts` (Firebase Başlatma):**
  - Eğer `VITE_DATA_SOURCE_TYPE === 'firebase'`, `VITE_FIREBASE_...` ayarlarını kullanarak Firebase app'ini başlatır.
  - `firebaseApp` (veya `auth`, `db`, `storage`) export eder/provide eder.
  - `quasar.config.js`'e eklenir.
- [ ] **`src/core/data-layer/implementations/rest/restApiService.ts`:**
  - `IDataService`'i Axios kullanarak bir REST API üzerinden implemente eder.
  - Koşul: `import.meta.env.VITE_DATA_SOURCE_TYPE === 'restapi'`.
  - Import: `IDataService`, `axiosInstance` (aşağıdaki boot dosyasından).
- [ ] **`src/boot/axios.ts` (Axios Yapılandırması):**
  - Axios instance'ını oluşturur (`VITE_API_BASE_URL`, `VITE_API_TIMEOUT`).
  - Global interceptor'lar (token ekleme, `notificationService.apiError` ile hata yönetimi).
  - `axiosInstance` export eder.
  - `quasar.config.js`'e eklenir.
- [ ] **`src/core/data-layer/implementations/mock/mockDataService.ts` (Opsiyonel):**
  - `IDataService`'i lokal mock verilerle implemente eder.
  - Koşul: `import.meta.env.VITE_DATA_SOURCE_TYPE === 'mock'`.
- [ ] **`src/core/data-layer/dataServiceProvider.ts` (Factory):**
  - `import.meta.env.VITE_DATA_SOURCE_TYPE`'a göre uygun `IDataService` implementasyonunu oluşturan `getDataService(): IDataService` fonksiyonu sağlar.
- [ ] **`src/boot/data-service.ts` (Veri Servisini Enjekte Etme):**
  - `getDataService()`'i çağırır, `IDataService` örneğini Vue app'e `provide` eder ve/veya export eder.
  - `quasar.config.js`'e eklenir (diğer bağımlı boot dosyalarından sonra).

## 4. Çekirdek Modüller ve Özellikler

### 4.1. Çoklu Dil (i18n) Yapılandırması

- [ ] **`src/i18n/index.ts` (Ana i18n Yapılandırma Dosyası):** Genel ve modül bazlı dil dosyalarını birleştirir, `messages` objesini export eder.
- [ ] **Genel Dil Dosyaları (örn: `src/i18n/en-US/index.ts`, `src/i18n/tr-TR/index.ts`):** Uygulama geneli paylaşılan çevirileri içerir.
- [ ] **`src/boot/i18n.ts` (Vue-i18n Başlatma):**
  - `createI18n` ile `vue-i18n` örneğini oluşturur.
  - `VITE_DEFAULT_LOCALE`, `VITE_FALLBACK_LOCALE` ayarlarını kullanır.
  - `messages`'ı (`src/i18n/index.ts`'den) yükler.
  - Vue app'e ve Quasar'a entegre eder. `i18n` örneğini export eder.
  - `quasar.config.js`'e eklenir.
- [ ] **Dil Değiştirme Bileşeni (`src/components/LanguageSwitcher.vue`):** `i18n.global.locale`'i günceller, tercihi `localStorage`'da saklar.

### 4.2. Çoklu Tema (Theme) Yönetimi

- [ ] **`src/core/theme/theme.service.ts` (veya `src/composables/useTheme.ts`):**
  - Temayı değiştirme, mevcut temayı alma, başlangıç temasını (`VITE_DEFAULT_THEME`, `localStorage`) uygulama mantığını içerir.
  - Quasar `Dark` modülünü ve/veya CSS özel özelliklerini yönetir.
- [ ] **`src/stores/theme.store.ts` (Pinia Store - Opsiyonel):** Aktif tema durumunu tutar, tema değiştirme action'ını içerir.
- [ ] **`src/boot/theme.ts`:** Uygulama başlangıcında başlangıç temasını yükler/ayarlar.
  - `quasar.config.js`'e eklenir.
- [ ] **Tema Değiştirme Bileşeni (`src/components/ThemeSwitcher.vue`):** Tema (light/dark) değiştirmeyi sağlar.
- [ ] **Quasar Tema Yapılandırması (`src/css/quasar.variables.scss`):** Tema renklerini ve stillerini ayarlar.

### 4.3. Kimlik Doğrulama (Auth) Modülü (`src/modules/auth/`)

- [ ] **Dizin Yapısı:** Standart modül yapısını oluşturun.
- [ ] **`types/IUser.ts`, `types/AuthCredentials.ts` vb. modüle özel tipler.**
- [ ] **`services/AuthService.ts`:**
  - Login, logout, register, getCurrentUser, isAuthenticated işlemlerini yönetir.
  - Enjekte edilen `IDataService`'i (veya Firebase Auth'u doğrudan) kullanır. Token'ları yönetir.
- [ ] **`stores/auth.store.ts` (Pinia Store):**
  - Kullanıcı bilgisi, token, kimlik doğrulama durumu, yüklenme durumu gibi state'leri tutar.
  - `AuthService`'i kullanarak login/logout/register/autoLogin gibi action'ları içerir.
- [ ] **`pages/LoginPage.vue`, `RegisterPage.vue` (opsiyonel), `ForgotPasswordPage.vue` (opsiyonel).**
- [ ] **`router/index.ts` (Auth Modülü Rotaları):** Modülün rotalarını export eder.
- [ ] **`i18n/en-US.ts`, `tr-TR.ts` (Auth Modülü Çevirileri).**
- [ ] **Auth Servisi ve Store'un Uygulamaya Entegrasyonu:** `autoLogin` action'ı uygulama başlangıcında çağrılır.

## 5. Ana Router ve Navigasyon Korumaları

- [ ] **`src/router/routes.ts` (Ana Rota Tanımları):**
  - Temel layout'ları ve genel sayfaları tanımlar. Modül rotalarını import edip birleştirir.
- [ ] **`src/router/index.ts` (Vue Router Instance):**
  - `createRouter` ile Vue Router örneğini oluşturur.
  - Navigasyon korumalarını (`beforeEach`) tanımlar (`meta: { requiresAuth: true }`, `meta: { guestOnly: true }`).
  - `useAuthStore`'u kullanarak kimlik doğrulama durumunu kontrol eder.
  - `src/main.ts`'de `app.use(router)` ile kullanılır.

## 6. Örnek Bir Özellik Modülü (Örn: Todo List - `src/modules/todo/`)

- [ ] **Dizin Yapısı:** Standart modül yapısını oluşturun.
- [ ] **`types/ITodo.ts`:** Todo veri modelini tanımlar.
- [ ] **`services/TodoService.ts`:** Todo'lar için CRUD operasyonlarını sağlar, `IDataService`'i kullanır.
- [ ] **`stores/todo.store.ts` (Pinia Store):** Todo listesini, yüklenme/hata durumlarını tutar. `TodoService`'i kullanarak action'ları implemente eder.
- [ ] **`components/TodoList.vue`, `TodoItem.vue`, `AddTodoForm.vue`.**
- [ ] **`pages/TodoPage.vue`:** Todo bileşenlerini ve store'u kullanır. `meta: { requiresAuth: true }` olabilir.
- [ ] **`router/index.ts`:** Todo sayfası için route tanımı.
- [ ] **`i18n/en-US.ts`, `tr-TR.ts`:** Modüle özel çeviriler.
- [ ] **Ana Router'a Entegrasyon:** `src/router/routes.ts` dosyasına todo modülünün rotalarını ekleyin.

## 7. SOLID Prensipleri, Mikro Yaklaşımlar ve Kod Tekrarı Üzerine Sürekli Dikkat

- [ ] Tek Sorumluluk Prensibi (SRP)
- [ ] Açık/Kapalı Prensibi (OCP)
- [ ] Liskov Yerine Geçme Prensibi (LSP)
- [ ] Arayüz Ayrımı Prensibi (ISP)
- [ ] Bağımlılıkların Tersine Çevrilmesi Prensibi (DIP)
- [ ] Kod Tekrarından Kaçınma (DRY)
- [ ] Mikro Komponentler/Servisler/Arayüzler

## 8. Test ve Kalite Güvencesi

- [ ] **Birim Testleri (Vitest/Jest):** Servisler, store'lar, composable'lar, utility fonksiyonları.
- [ ] **Bileşen Testleri (Quasar Testing Harness):** Mikro ve özellik bileşenleri.
- [ ] **Uçtan Uca Testler (E2E Tests - Cypress/Playwright):** Ana kullanıcı akışları.
- [ ] **Linting ve Formatlama (Husky + lint-staged ile pre-commit hook'ları).**

## 9. Dokümantasyon

- [ ] **README.md (Proje Geneli).**
- [ ] **Modül Bazlı README dosyaları.**
- [ ] **Kod İçi Yorumlar (JSDoc/TSDoc).**
- [ ] **Mimari Kararların Dokümantasyonu (ADR - Architecture Decision Records - Opsiyonel).**

## 10. Sürekli Geliştirme ve İyileştirmeler

- [ ] Düzenli Code Review'lar.
- [ ] Iteratif Geliştirme.
- [ ] Refactoring.
- [ ] Performans Optimizasyonları.
- [ ] Erişilebilirlik (a11y) Kontrolleri.
