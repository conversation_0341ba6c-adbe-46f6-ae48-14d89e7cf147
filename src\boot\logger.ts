import { boot } from 'quasar/wrappers';
import type { ILoggerService } from 'src/services/logger';
import { loggerService } from 'src/services/logger';
import { inject } from 'vue';

// Logger servisi için injection key'i tanımla
// Symbol kullanmak, çakışmaları önler ve tip güvenliği sağlar
export const LoggerServiceKey = Symbol('LoggerService');

export default boot(({ app }) => {
  // Logger servisini uygulama genelinde provide et
  // Bu sayede herhangi bir bileşen veya composable'dan inject edilebilir
  app.provide(LoggerServiceKey, loggerService);
});

/**
 * @description Uygulama genelinde provide edilen logger servisini inject etmek için composable.
 * @returns ILoggerService örneği.
 * @throws Error Eğer logger servisi provide edilmemişse hata fırlatır.
 */
export function useLogger(): ILoggerService {
  const logger = inject<ILoggerService>(LoggerServiceKey);
  // console.info('Logger servisi başlıyor!');

  if (!logger) {
    // Geliştirme sırasında provide edilmediğinde uyarı vermek faydalı olabilir
    console.error('Logger servisi provide edilmemiş!');
    throw new Error('Logger servisi provide edilmemiş!');
  }

  return logger;
}
