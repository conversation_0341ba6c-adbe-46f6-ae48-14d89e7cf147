/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_NAME: string;
  readonly VITE_APP_VERSION: string;
  readonly VITE_DATA_SOURCE_TYPE: 'firebase' | 'restapi' | 'mock';
  readonly VITE_API_BASE_URL?: string; // REST API için opsiyonel
  readonly VITE_API_TIMEOUT?: string; // REST API için opsiyonel
  readonly VITE_FIREBASE_API_KEY?: string; // Firebase için opsiyonel
  readonly VITE_FIREBASE_AUTH_DOMAIN?: string; // Firebase için opsiyonel
  readonly VITE_FIREBASE_PROJECT_ID?: string; // Firebase için opsiyonel
  readonly VITE_FIREBASE_STORAGE_BUCKET?: string; // Firebase için opsiyonel
  readonly VITE_FIREBASE_MESSAGING_SENDER_ID?: string; // Firebase için opsiyonel
  readonly VITE_FIREBASE_APP_ID?: string; // Firebase için opsiyonel
  readonly VITE_FIREBASE_MEASUREMENT_ID?: string; // Firebase için opsiyonel (Opsiyonel)
  readonly VITE_DEFAULT_LOCALE: string;
  readonly VITE_FALLBACK_LOCALE: string;
  readonly VITE_DEFAULT_THEME: 'light' | 'dark' | 'auto';
  readonly VITE_LOG_LEVEL: 'error' | 'warn' | 'info' | 'debug' | 'trace';
  readonly VITE_NOTIFICATION_DISPLAY_LEVEL: 'error' | 'warn' | 'info' | 'success' | 'none';
  readonly VITE_API_ERROR_NOTIFICATION_TYPE: 'toast' | 'friendly' | 'silent';
  readonly VITE_NOTIFICATION_DEFAULT_POSITION:
    | 'top-left'
    | 'top-right'
    | 'bottom-left'
    | 'bottom-right'
    | 'top'
    | 'bottom'
    | 'left'
    | 'right'
    | 'center'; // Quasar Notify pozisyonları
  readonly VITE_NOTIFICATION_DEFAULT_TIMEOUT: string;
  readonly VITE_NOTIFICATION_DEFAULT_GROUP: string; // "true" | "false" olarak string
  readonly VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// Quasar Notify pozisyonları için tip tanımı (isteğe bağlı, daha okunabilir kod için)
type QNotifyPosition = ImportMetaEnv['VITE_NOTIFICATION_DEFAULT_POSITION'];
