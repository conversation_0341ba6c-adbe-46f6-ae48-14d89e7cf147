import { ConsoleLoggerService } from './logger.service';
import type { ILoggerService } from './logger.interface';

// Ortam değişkenine göre logger servisi örneğini oluştur
// process.env.NODE_ENV Quasar tarafından sağlanır
const isProduction = process.env.NODE_ENV === 'production';
const loggerService: ILoggerService = new ConsoleLoggerService(isProduction);

/**
 * @description Uygulama genelinde kullanılacak logger servisi örneği.
 * ILoggerService arayüzünü uygular.
 */
export { loggerService };
export type { ILoggerService };
