/**
 * @description Tüm veri modelleri için temel arayüz.
 * Ortak al<PERSON>ı (ID, zaman damgaları, aktiflik durumu vb.) tanımlar.
 */

/**
 * @description Kayıtların benzersiz tanımlayıcısı için tip tanımı.
 */
export type ID = string | number;

export interface IBaseModel {
  /**
   * @description Kaydın benzersiz tanımlayıcısı.
   */
  id: ID;

  /**
   * @description Kaydın oluşturulma zamanı (isteğe bağlı).
   */
  createdAt?: Date;

  /**
   * @description Kaydın son güncellenme zamanı (isteğe bağlı).
   */
  updatedAt?: Date;

  /**
   * @description Kaydın aktiflik durumu (isteğe bağlı).
   */
  isActive?: boolean;
}
