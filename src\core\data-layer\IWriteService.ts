import type { IBaseModel, ID } from './IBaseModel';

/**
 * @description Veri kaynağına yazma operasyonları için temel arayüz.
 * @template T - Üzerinde işlem yapılacak veri modelinin tipi. IBaseModel arayüzünü genişletmelidir.
 */
export interface IWriteService<T extends IBaseModel> {
  /**
   * @description Veri kaynağına yeni bir kayıt ekler.
   * @param {T} item - Eklenecek kayıt.
   * @returns {Promise<T>} Eklenen kaydı içeren Promise.
   */
  create(item: T): Promise<T>;

  /**
   * @description Belirtilen ID'ye sahip kaydı günceller.
   * @param {ID} id - Güncellenecek kaydın ID'si.
   * @param {T} item - Güncel veri.
   * @returns {Promise<T | undefined>} Güncellenmiş kaydı içeren Promise veya bulunamazsa undefined.
   */
  update(id: ID, item: T): Promise<T | undefined>;

  /**
   * @description Belirtilen ID'ye sahip kaydı siler.
   * @param {ID} id - Silinecek kaydın ID'si.
   * @returns {Promise<void>} Silme işleminin tamamlandığını belirten Promise.
   */
  delete(id: ID): Promise<void>;
}
