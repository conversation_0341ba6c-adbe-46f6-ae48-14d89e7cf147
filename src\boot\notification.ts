// src/boot/notification.ts
import { boot } from 'quasar/wrappers';
import { NotificationService } from 'src/services/notification/notification.service';
import type { INotificationService } from 'src/services/notification/notification.interface';

// Servis örneğini oluştur
const notificationService: INotificationService = new NotificationService();

// Vue uygulamasına enjekte et
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $notification: INotificationService;
  }
}

export default boot(({ app }) => {
  app.config.globalProperties.$notification = notificationService;
});

// Composable içinde kullanmak için servisi dışa aktar
export { notificationService };
