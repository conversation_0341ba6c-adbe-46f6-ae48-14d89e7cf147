import type { IDataService } from '../../IDataService';
import type { IBaseModel, ID } from '../../IBaseModel';
import { db } from '../../../../boot/firebase'; // Firebase db instance
import type {
  DocumentData,
  QuerySnapshot,
  DocumentSnapshot,
  WhereFilterOp,
  Query,
  WriteBatch,
  OrderByDirection,
  Firestore,
} from 'firebase/firestore';
import {
  collection,
  doc,
  getDocs,
  getDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  serverTimestamp,
  Timestamp,
  query,
  where,
  writeBatch,
  limit,
  orderBy,
} from 'firebase/firestore';

/**
 * @description Firestore Timestamp'ini JavaScript Date objesine dönüştürür.
 * @param {Timestamp | undefined} timestamp - Firestore Timestamp.
 * @returns {Date | undefined} JavaScript Date objesi veya undefined.
 */
const fromFirestoreTimestamp = (timestamp: Timestamp | undefined): Date | undefined => {
  return timestamp instanceof Timestamp ? timestamp.toDate() : undefined;
};

/**
 * @description JavaScript Date objesini Firestore Timestamp'ine dönüştürür.
 * @param {Date | undefined} date - JavaScript Date objesi.
 * @returns {Timestamp | undefined} Firestore Timestamp veya undefined.
 */
const toFirestoreTimestamp = (date: Date | undefined): Timestamp | undefined => {
  return date instanceof Date ? Timestamp.fromDate(date) : undefined;
};

/**
 * @description Firestore DocumentData'yı IBaseModel'e dönüştürür.
 * @template T - IBaseModel arayüzünü genişleten tip.
 * @param {DocumentSnapshot<DocumentData>} doc - Firestore DocumentSnapshot.
 * @returns {T | undefined} IBaseModel tipinde obje veya undefined.
 */
const fromFirestoreDocument = <T extends IBaseModel>(
  doc: DocumentSnapshot<DocumentData>,
): T | undefined => {
  if (!doc.exists()) {
    return undefined;
  }
  const data = doc.data();
  if (!data) {
    return undefined;
  }

  // Tarih alanlarını dönüştür
  const item: T = {
    id: doc.id,
    ...data,
    createdAt: fromFirestoreTimestamp(data.createdAt),
    updatedAt: fromFirestoreTimestamp(data.updatedAt),
  } as T;

  return item;
};

/**
 * @description IBaseModel'i Firestore DocumentData'ya dönüştürür.
 * @template T - IBaseModel arayüzünü genişleten tip.
 * @param {T} item - IBaseModel tipinde obje.
 * @returns {DocumentData} Firestore DocumentData.
 */
const toFirestoreDocument = <T extends IBaseModel>(item: T): DocumentData => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { id, createdAt, updatedAt, ...rest } = item;
  const data: DocumentData = {
    ...rest,
    createdAt: createdAt ? toFirestoreTimestamp(createdAt) : serverTimestamp(),
    updatedAt: serverTimestamp(),
  };
  return data;
};

/**
 * @description Sorgu filtresi için kullanılacak arayüz
 */
export interface QueryFilter {
  field: string;
  operator: WhereFilterOp;
  value: unknown;
}

/**
 * @description Sorgu sıralama seçeneği için kullanılacak arayüz
 */
export interface QueryOrder {
  field: string;
  direction: OrderByDirection;
}

/**
 * @description Sorgu seçenekleri için kullanılacak arayüz
 */
export interface QueryOptions {
  filters?: QueryFilter[];
  orderBy?: QueryOrder[];
  limitCount?: number;
}

/**
 * @description Firebase Firestore kullanarak IDataService arayüzünü implemente eder.
 * @template T - Üzerinde işlem yapılacak veri modelinin tipi. IBaseModel arayüzünü genişletmelidir.
 */
export class FirebaseDataService<T extends IBaseModel> implements IDataService<T> {
  private collectionName: string;
  private firestore: Firestore;

  /**
   * @param {string} collectionName - Firestore koleksiyon adı.
   */
  constructor(collectionName: string) {
    this.collectionName = collectionName;
    this.checkDbConnection();
    this.firestore = db as Firestore;
  }

  /**
   * @description Veritabanı bağlantısını kontrol eder.
   * @private
   * @throws {Error} Veritabanı başlatılmadıysa hata fırlatır.
   */
  private checkDbConnection(): void {
    if (!db) {
      throw new Error('Firestore veritabanı başlatılmadı.');
    }
  }

  /**
   * @description ID'nin string olup olmadığını kontrol eder.
   * @private
   * @param {ID} id - Kontrol edilecek ID.
   * @throws {Error} ID string değilse hata fırlatır.
   */
  private validateStringId(id: ID): string {
    if (typeof id !== 'string') {
      throw new Error(`Geçersiz ID tipi: ${typeof id}. Firestore belge ID'leri string olmalıdır.`);
    }
    return id;
  }

  /**
   * @description Veri kaynağından tüm kayıtları asenkron olarak getirir.
   * @returns {Promise<T[]>} Tüm kayıtları içeren Promise.
   */
  async getAll(): Promise<T[]> {
    return this.query();
  }

  /**
   * @description Belirtilen sorgu seçeneklerine göre kayıtları asenkron olarak getirir.
   * @param {QueryOptions} options - Sorgu seçenekleri.
   * @returns {Promise<T[]>} Sorgu sonuçlarını içeren Promise.
   */
  async query(options?: QueryOptions): Promise<T[]> {
    this.checkDbConnection();

    const colRef = collection(this.firestore, this.collectionName);
    let q: Query = colRef;

    if (options?.filters?.length) {
      options.filters.forEach((filter) => {
        q = query(q, where(filter.field, filter.operator, filter.value));
      });
    }

    if (options?.orderBy?.length) {
      options.orderBy.forEach((order) => {
        q = query(q, orderBy(order.field, order.direction));
      });
    }

    if (options?.limitCount) {
      q = query(q, limit(options.limitCount));
    }

    const snapshot: QuerySnapshot<DocumentData> = await getDocs(q);

    return snapshot.docs
      .map((doc) => fromFirestoreDocument<T>(doc))
      .filter((item): item is T => item !== undefined);
  }

  /**
   * @description Belirtilen ID'ye sahip kaydı asenkron olarak getirir.
   * @param {ID} id - Getirilecek kaydın ID'si.
   * @returns {Promise<T | undefined>} Kaydı içeren Promise veya bulunamazsa undefined.
   */
  async getById(id: ID): Promise<T | undefined> {
    try {
      this.checkDbConnection();
      const stringId = this.validateStringId(id);

      const docRef = doc(this.firestore, this.collectionName, stringId);
      const snapshot: DocumentSnapshot<DocumentData> = await getDoc(docRef);

      return fromFirestoreDocument<T>(snapshot);
    } catch (error) {
      console.error(`getById(${id}) işlemi sırasında hata:`, error);
      return undefined;
    }
  }

  /**
   * @description Veri kaynağına yeni bir kayıt ekler.
   * @param {T} item - Eklenecek kayıt. ID alanı göz ardı edilir.
   * @returns {Promise<T>} Eklenen kaydı içeren Promise.
   */
  async create(item: T): Promise<T> {
    this.checkDbConnection();

    // ID'yi çıkar, Firestore otomatik oluşturacak
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { id, ...itemWithoutId } = item;
    const docData = toFirestoreDocument(itemWithoutId as T);

    const colRef = collection(this.firestore, this.collectionName);
    const docRef = await addDoc(colRef, docData);

    // Eklenen kaydı getir
    const snapshot = await getDoc(docRef);
    const createdItem = fromFirestoreDocument<T>(snapshot);

    if (!createdItem) {
      throw new Error("Oluşturulan kayıt Firestore'dan alınamadı.");
    }

    return createdItem;
  }

  /**
   * @description Belirtilen ID'ye sahip kaydı günceller.
   * @param {ID} id - Güncellenecek kaydın ID'si.
   * @param {T} item - Güncel veri.
   * @returns {Promise<T | undefined>} Güncellenmiş kaydı içeren Promise veya bulunamazsa undefined.
   */
  async update(id: ID, item: T): Promise<T | undefined> {
    try {
      this.checkDbConnection();
      const stringId = this.validateStringId(id);

      const docRef = doc(this.firestore, this.collectionName, stringId);
      const docData = toFirestoreDocument(item);

      // Belge var mı kontrol et
      const snapshot = await getDoc(docRef);
      if (!snapshot.exists()) {
        return undefined;
      }

      await updateDoc(docRef, docData);

      // Güncellenmiş kaydı getir
      const updatedSnapshot = await getDoc(docRef);
      return fromFirestoreDocument<T>(updatedSnapshot);
    } catch (error) {
      console.error(`update(${id}) işlemi sırasında hata:`, error);
      return undefined;
    }
  }

  /**
   * @description Belirtilen ID'ye sahip kaydı siler.
   * @param {ID} id - Silinecek kaydın ID'si.
   * @returns {Promise<void>} İşlemin tamamlandığını belirten Promise.
   */
  async delete(id: ID): Promise<void> {
    try {
      this.checkDbConnection();
      const stringId = this.validateStringId(id);

      const docRef = doc(this.firestore, this.collectionName, stringId);
      await deleteDoc(docRef);
    } catch (error) {
      console.error(`delete(${id}) işlemi sırasında hata:`, error);
      throw error;
    }
  }

  /**
   * @description Birden fazla kaydı toplu olarak ekler.
   * @param {T[]} items - Eklenecek kayıtlar.
   * @returns {Promise<void>} İşlemin tamamlandığını belirten Promise.
   */
  async createBatch(items: T[]): Promise<void> {
    if (!items.length) return;

    this.checkDbConnection();
    const batch: WriteBatch = writeBatch(this.firestore);

    items.forEach((item) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { id, ...itemWithoutId } = item;
      const docData = toFirestoreDocument(itemWithoutId as T);
      const newDocRef = doc(collection(this.firestore, this.collectionName));
      batch.set(newDocRef, docData);
    });

    await batch.commit();
  }

  /**
   * @description Birden fazla kaydı toplu olarak günceller.
   * @param {Array<{id: ID, item: T}>} updates - Güncellenecek kayıtlar ve ID'leri.
   * @returns {Promise<void>} İşlemin tamamlandığını belirten Promise.
   */
  async updateBatch(updates: Array<{ id: ID; item: T }>): Promise<void> {
    if (!updates.length) return;

    this.checkDbConnection();
    const batch: WriteBatch = writeBatch(this.firestore);

    updates.forEach(({ id, item }) => {
      if (typeof id !== 'string') {
        console.error(`Geçersiz ID tipi: ${typeof id}. Bu kayıt atlanacak.`);
        return;
      }

      const docRef = doc(this.firestore, this.collectionName, id);
      const docData = toFirestoreDocument(item);
      batch.update(docRef, docData);
    });

    await batch.commit();
  }

  /**
   * @description Birden fazla kaydı toplu olarak siler.
   * @param {ID[]} ids - Silinecek kayıtların ID'leri.
   * @returns {Promise<void>} İşlemin tamamlandığını belirten Promise.
   */
  async deleteBatch(ids: ID[]): Promise<void> {
    if (!ids.length) return;

    this.checkDbConnection();
    const batch: WriteBatch = writeBatch(this.firestore);

    ids.forEach((id) => {
      if (typeof id !== 'string') {
        console.error(`Geçersiz ID tipi: ${typeof id}. Bu kayıt atlanacak.`);
        return;
      }

      const docRef = doc(this.firestore, this.collectionName, id);
      batch.delete(docRef);
    });

    await batch.commit();
  }
}
