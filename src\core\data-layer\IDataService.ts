import type { IBaseModel } from './IBaseModel';
import type { IReadService } from './IReadService';
import type { IWriteService } from './IWriteService';

/**
 * @description Çekirdek veri katmanı için temel arayüz.
 * IReadService ve IWriteService arayüzlerini birleştirerek tüm CRUD operasyonlarını sağlar.
 * @template T - Üzerinde işlem yapılacak veri modelinin tipi. IBaseModel arayüzünü genişletmelidir.
 */
export interface IDataService<T extends IBaseModel> extends IReadService<T>, IWriteService<T> {}
