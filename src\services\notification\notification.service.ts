// src/services/notification/notification.service.ts
import type { QNotifyCreateOptions } from 'quasar';
import { Notify } from 'quasar';
import type { INotificationService } from './notification.interface';

// Ortam değişkenlerinden varsayılan ayarları al
const DEFAULT_NOTIFICATION_OPTIONS: QNotifyCreateOptions = {
  position:
    (process.env.VITE_NOTIFICATION_DEFAULT_POSITION as QNotifyCreateOptions['position']) ||
    'bottom-right',
  timeout: process.env.VITE_NOTIFICATION_DEFAULT_TIMEOUT
    ? parseInt(process.env.VITE_NOTIFICATION_DEFAULT_TIMEOUT, 10)
    : 5000,
  group: process.env.VITE_NOTIFICATION_DEFAULT_GROUP === 'true',
  closeBtn: process.env.VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL === 'true', // Vars<PERSON><PERSON>lan olarak boolean kabul edildi
};

export class NotificationService implements INotificationService {
  show(options: QNotifyCreateOptions): void {
    const finalOptions = {
      ...DEFAULT_NOTIFICATION_OPTIONS,
      ...options,
    };
    Notify.create(finalOptions);
  }

  success(message: string, options?: QNotifyCreateOptions): void {
    const finalOptions = {
      message,
      type: 'positive',
      ...DEFAULT_NOTIFICATION_OPTIONS,
      ...options,
    };
    Notify.create(finalOptions);
  }

  error(message: string, options?: QNotifyCreateOptions): void {
    const finalOptions = {
      message,
      type: 'negative',
      ...DEFAULT_NOTIFICATION_OPTIONS,
      ...options,
    };
    Notify.create(finalOptions);
  }

  warning(message: string, options?: QNotifyCreateOptions): void {
    const finalOptions = {
      message,
      type: 'warning',
      ...DEFAULT_NOTIFICATION_OPTIONS,
      ...options,
    };
    Notify.create(finalOptions);
  }

  info(message: string, options?: QNotifyCreateOptions): void {
    const finalOptions = {
      message,
      type: 'info',
      ...DEFAULT_NOTIFICATION_OPTIONS,
      ...options,
    };
    Notify.create(finalOptions);
  }
}
