import type { IBaseModel, ID } from './IBaseModel';

/**
 * @description Veri kaynağından okuma operasyonları için temel arayüz.
 * @template T - Üzerinde işlem yapılacak veri modelinin tipi. IBaseModel arayüzünü genişletmelidir.
 */
export interface IReadService<T extends IBaseModel> {
  /**
   * @description Veri kaynağından tüm kayıtları asenkron olarak getirir.
   * @returns {Promise<T[]>} Tüm kayıtları içeren Promise.
   */
  getAll(): Promise<T[]>;

  /**
   * @description Belirtilen ID'ye sahip kaydı asenkron olarak getirir.
   * @param {ID} id - Getirilecek kaydın ID'si.
   * @returns {Promise<T | undefined>} Kaydı içeren Promise veya bulunamazsa undefined.
   */
  getById(id: ID): Promise<T | undefined>;
}
