import type { ILoggerService } from './logger.interface';

/**
 * @description Temel console tabanlı Logger Servisi implementasyonu.
 * Geliştirme ortamında tüm logları, üretim ortamında ise sadece uyarı ve hata loglarını console'a yazar.
 */
export class ConsoleLoggerService implements ILoggerService {
  private isProduction: boolean;

  constructor(isProduction: boolean) {
    this.isProduction = isProduction;
  }

  /**
   * @description Bilgilendirme mesajı loglar.
   * Geliştirme ortamında console.info kullanır.
   * @param message Loglanacak mesaj.
   * @param context Opsiyonel ek bağlam verisi.
   */
  info(message: string, context?: unknown): void {
    if (!this.isProduction) {
      console.info(`💩[INFO] ${message}`, context);
    }
  }

  /**
   * @description Uyarı mesajı loglar.
   * console.warn kullanır.
   * @param message Loglanacak mesaj.
   * @param context Opsiyonel ek bağlam verisi.
   */
  warn(message: string, context?: unknown): void {
    console.warn(`[WARN] ${message}`, context);
  }

  /**
   * @description Hata mesajı loglar.
   * console.error kullanır.
   * @param message Loglanacak mesaj.
   * @param error Opsiyonel hata nesnesi.
   * @param context Opsiyonel ek bağlam verisi.
   */
  error(message: string, error?: Error, context?: unknown): void {
    console.error(`[ERROR] ${message}`, error, context);
  }

  /**
   * @description Debug mesajı loglar.
   * Sadece geliştirme ortamında console.debug kullanır.
   * @param message Loglanacak mesaj.
   * @param context Opsiyonel ek bağlam verisi.
   */
  debug(message: string, context?: unknown): void {
    if (!this.isProduction) {
      console.debug(`[DEBUG] ${message}`, context);
    }
  }
}
