# Quasar Notify Plugin Yapılandırması ve Notifikasyon Servisi Planı

Bu plan, Quasar uygulamasında notifikasyon yönetimini merkezi hale getirmek, kod tekrarını önlemek ve ortam değişkenlerini kullanarak esnek yapılandırma sağlamak amacıyla Quasar Notify pluginini yapılandırmayı ve özel bir notifikasyon servisi oluşturmayı detaylandırmaktadır. Plan, SOLID prensiplerine ve TypeScript disiplinine uygun olarak hazırlanmıştır.

## Hedefler

- Quasar Notify pluginini etkinleştirmek.
- Notifikasyon servisi için bir TypeScript arayüzü tanımlamak.
- `INotificationService` arayüzünü uygulayan bir `NotificationService` sınıfı oluşturmak.
- `.env.local` dosyasındaki ortam değişkenlerini kullanarak notifikasyonların varsayılan davranışını yapılandırmak.
- Notifikasyon servisini bir Quasar boot dosyası aracılığıyla Vue uygulamasına enjekte etmek.
- Servise kolay erişim sağlayan bir `useNotification` composable fonksiyonu oluşturmak.

## Plan Detayları

### Adım 1: Quasar Notify Plugin Yapılandırması

- **Dosya:** [`quasar.config.ts`](quasar.config.ts)
- **Açıklama:** Quasar uygulamasının genel yapılandırma dosyasında Notify pluginini etkinleştireceğiz. Ortam değişkenlerini servis içinde yöneteceğimiz için burada varsayılan ayarları yapmayacağız.
- **Yapılacak:** `framework` altındaki `plugins` dizisine `'Notify'` ekle.

### Adım 2: Notifikasyon Servisi Arayüzü Tanımlama

- **Dosya:** [`src/services/notification/notification.interface.ts`](src/services/notification/notification.interface.ts) (Yeni dosya)
- **Açıklama:** Notifikasyon servisimizin sunacağı fonksiyonları ve alacağı parametreleri tanımlayan bir TypeScript `interface` oluşturulacak. Bu, servisimizin sözleşmesini belirler ve Bağımlılıkların Tersine Çevrilmesi Prensibi (DIP) için temel oluşturur.
- **Yapılacak:** `QNotifyCreateOptions` tipini kullanarak notifikasyon gösterme ve farklı tiplerdeki notifikasyonlar için metotları içeren `INotificationService` arayüzünü tanımla.

```typescript
// src/services/notification/notification.interface.ts
import { QNotifyCreateOptions } from 'quasar';

export interface INotificationService {
  show(options: QNotifyCreateOptions): void;
  success(message: string, options?: QNotifyCreateOptions): void;
  error(message: string, options?: QNotifyCreateOptions): void;
  warning(message: string, options?: QNotifyCreateOptions): void;
  info(message: string, options?: QNotifyCreateOptions): void;
}
```

### Adım 3: Notifikasyon Servisi Sınıfı Oluşturma (Ortam Değişkenleri Kullanımı)

- **Dosya:** [`src/services/notification/notification.service.ts`](src/services/notification/notification.service.ts) (Yeni dosya)
- **Açıklama:** `INotificationService` arayüzünü uygulayan somut `NotificationService` sınıfı oluşturulacak. Bu sınıf, Quasar'ın `Notify.create` metodunu kullanacak ve `.env.local` dosyasındaki ortam değişkenlerini okuyarak varsayılan notifikasyon seçeneklerini belirleyecektir. Kullanıcıdan gelen seçenekler, varsayılanları ezecektir.
- **Yapılacak:**
  - `.env.local` dosyasındaki `VITE_NOTIFICATION_DEFAULT_POSITION`, `VITE_NOTIFICATION_DEFAULT_TIMEOUT`, `VITE_NOTIFICATION_DEFAULT_GROUP`, `VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL` değişkenlerini oku.
  - Bu değişkenleri kullanarak varsayılan notifikasyon seçeneklerini içeren bir obje oluştur.
  - `show`, `success`, `error`, `warning`, `info` metotlarını uygula. Bu metotlar içinde `Notify.create` çağrılırken, varsayılan seçenekler ile kullanıcıdan gelen seçenekleri birleştirerek kullan.

```typescript
// src/services/notification/notification.service.ts
import { Notify, QNotifyCreateOptions } from 'quasar';
import { INotificationService } from './notification.interface';

// Ortam değişkenlerinden varsayılan ayarları al
const DEFAULT_NOTIFICATION_OPTIONS: QNotifyCreateOptions = {
  position:
    (process.env.VITE_NOTIFICATION_DEFAULT_POSITION as QNotifyCreateOptions['position']) ||
    'top-right',
  timeout: process.env.VITE_NOTIFICATION_DEFAULT_TIMEOUT
    ? parseInt(process.env.VITE_NOTIFICATION_DEFAULT_TIMEOUT, 10)
    : 5000,
  group: process.env.VITE_NOTIFICATION_DEFAULT_GROUP === 'true',
  closeBtn: process.env.VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL === 'true', // Varsayılan olarak boolean kabul edildi
};

export class NotificationService implements INotificationService {
  show(options: QNotifyCreateOptions): void {
    const finalOptions = {
      ...DEFAULT_NOTIFICATION_OPTIONS,
      ...options,
    };
    Notify.create(finalOptions);
  }

  success(message: string, options?: QNotifyCreateOptions): void {
    const finalOptions = {
      message,
      type: 'positive',
      ...DEFAULT_NOTIFICATION_OPTIONS,
      ...options,
    };
    Notify.create(finalOptions);
  }

  error(message: string, options?: QNotifyCreateOptions): void {
    const finalOptions = {
      message,
      type: 'negative',
      ...DEFAULT_NOTIFICATION_OPTIONS,
      ...options,
    };
    Notify.create(finalOptions);
  }

  warning(message: string, options?: QNotifyCreateOptions): void {
    const finalOptions = {
      message,
      type: 'warning',
      ...DEFAULT_NOTIFICATION_OPTIONS,
      ...options,
    };
    Notify.create(finalOptions);
  }

  info(message: string, options?: QNotifyCreateOptions): void {
    const finalOptions = {
      message,
      type: 'info',
      ...DEFAULT_NOTIFICATION_OPTIONS,
      ...options,
    };
    Notify.create(finalOptions);
  }
}
```

### Adım 4: Notifikasyon Servisi Boot Dosyası Oluşturma

- **Dosya:** [`src/boot/notification.ts`](src/boot/notification.ts) (Yeni dosya)
- **Açıklama:** Quasar boot dosyası, uygulama başlatılırken notifikasyon servisi örneğini oluşturmak ve bunu Vue uygulamasına global olarak enjekte etmek için kullanılacak. Bu, servise uygulamanın herhangi bir yerinden `$notification` veya composable aracılığıyla erişilmesini sağlar.
- **Yapılacak:**
  - `NotificationService` sınıfını import et.
  - Servis örneğini oluştur.
  - `declare module` kullanarak `$notification` özelliğini TypeScript'e tanıt.
  - `boot` fonksiyonu içinde servisi `app.config.globalProperties.$notification` olarak ata.
  - Composable içinde kullanılmak üzere servis örneğini dışa aktar.

```typescript
// src/boot/notification.ts
import { boot } from 'quasar/wrappers';
import { NotificationService } from 'src/services/notification/notification.service';
import { INotificationService } from 'src/services/notification/notification.interface';

// Servis örneğini oluştur
const notificationService: INotificationService = new NotificationService();

// Vue uygulamasına enjekte et
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $notification: INotificationService;
  }
}

export default boot(({ app }) => {
  app.config.globalProperties.$notification = notificationService;
});

// Composable içinde kullanmak için servisi dışa aktar
export { notificationService };
```

### Adım 5: Servisi Kullanıma Hazırlama (Composable Güncelleme)

- **Dosya:** [`src/composables/useNotification.ts`](src/composables/useNotification.ts) (Yeni dosya)
- **Açıklama:** Notifikasyon servisine erişim için bir Composition API composable fonksiyonu oluşturulacak. Bu composable, boot dosyasında sağlanan servis örneğini kullanacaktır.
- **Yapılacak:**
  - `INotificationService` arayüzünü ve boot dosyasından dışa aktarılan `notificationService` örneğini import et.
  - `useNotification` fonksiyonunu tanımla ve `notificationService` örneğini döndür.

```typescript
// src/composables/useNotification.ts
import { INotificationService } from 'src/services/notification/notification.interface';
import { notificationService } from 'src/boot/notification'; // Boot dosyasından import et

export function useNotification(): INotificationService {
  return notificationService;
}
```

## Dosya Yapısı

Bu planın uygulanması sonucunda oluşacak dosya yapısı:

```
src/
├── boot/
│   ├── axios.ts
│   ├── i18n.ts
│   ├── logger.ts
│   └── notification.ts // Yeni dosya
├── composables/
│   └── useNotification.ts // Yeni dosya
├── services/
│   ├── logger/
│   │   ├── index.ts
│   │   ├── logger.interface.ts
│   │   └── logger.service.ts
│   └── notification/ // Yeni klasör
│       ├── notification.interface.ts // Yeni dosya
│       └── notification.service.ts // Yeni dosya
└── ...diğer dosyalar
```
