# Uygulama Adı
VITE_APP_NAME="Servis Takip By AI"

# Uygulama Versiyonu
VITE_APP_VERSION="1.0.0"

# Veri Kaynağı Tipi (firebase, restapi, mock)
VITE_DATA_SOURCE_TYPE="firebase"

# REST API Ayarları (Eğer VITE_DATA_SOURCE_TYPE="restapi")
VITE_API_BASE_URL="http://localhost:8080/api"
VITE_API_TIMEOUT="30000"

# Firebase Ayarları (Eğer VITE_DATA_SOURCE_TYPE="firebase")
VITE_FIREBASE_API_KEY="YOUR_FIREBASE_API_KEY_PLACEHOLDER"
VITE_FIREBASE_AUTH_DOMAIN="YOUR_FIREBASE_AUTH_DOMAIN_PLACEHOLDER"
VITE_FIREBASE_PROJECT_ID="YOUR_FIREBASE_PROJECT_ID_PLACEHOLDER"
VITE_FIREBASE_STORAGE_BUCKET="YOUR_FIREBASE_STORAGE_BUCKET_PLACEHOLDER"
VITE_FIREBASE_MESSAGING_SENDER_ID="YOUR_FIREBASE_MESSAGING_SENDER_ID_PLACEHOLDER"
VITE_FIREBASE_APP_ID="YOUR_FIREBASE_APP_ID_PLACEHOLDER"
VITE_FIREBASE_MEASUREMENT_ID="YOUR_FIREBASE_MEASUREMENT_ID_PLACEHOLDER" # Opsiyonel

# i18n Ayarları
VITE_DEFAULT_LOCALE="tr-TR"
VITE_FALLBACK_LOCALE="en-US"

# Tema Ayarları (light, dark, auto)
VITE_DEFAULT_THEME="light"

# Loglama Ayarları (error, warn, info, debug, trace)
VITE_LOG_LEVEL="debug"

# Notifikasyon Ayarları (Quasar Notify)
# Gösterilecek bildirim seviyesi (error, warn, info, success, none)
VITE_NOTIFICATION_DISPLAY_LEVEL="info"
# API hata bildirim tipi (toast, friendly, silent)
VITE_API_ERROR_NOTIFICATION_TYPE="friendly"
# Varsayılan bildirim pozisyonu (Quasar Notify pozisyonları)
VITE_NOTIFICATION_DEFAULT_POSITION="top-right"
# Varsayılan bildirim zaman aşımı (milisaniye)
VITE_NOTIFICATION_DEFAULT_TIMEOUT="5000"
# Bildirimleri gruplama ("true" | "false")
VITE_NOTIFICATION_DEFAULT_GROUP="true"
# Varsayılan kapatma düğmesi etiketi (Boş = Quasar varsayılanı, veya ikon/metin)
VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL=""