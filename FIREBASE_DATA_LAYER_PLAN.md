# Çekirdek Veri <PERSON>ı - Firebase Implementasyonu Planı

Bu belge, çekirdek veri katmanının Firebase (Firestore DB) kullanılarak implemente edilmesi ve Firebase uygulamasının başlatılması için izlenecek adımları detaylandırmaktadır.

## Hedef

- `IDataService` arayüzünü Firebase Firestore kullanarak implemente etmek.
- Firebase uygulamasını Quasar boot süreci sırasında başlatmak.

## Plan Adımları

1.  **Firebase Başlatma Boot Dosyasını Oluşturma:**

    - `src/boot/firebase.ts` adında yeni bir dosya oluşturulacak.
    - Bu dosya, ortam değişkeni `import.meta.env.VITE_DATA_SOURCE_TYPE` değeri `'firebase'` olduğunda Firebase uygulamasını başlatacak.
    - Firebase SDK'sından gerekli modüller (<PERSON><PERSON><PERSON><PERSON>, `initialize<PERSON>pp`, `getFirestore`, `getAuth`, `getStorage`) import edilecek.
    - `.env` dosyasında tanımlanacak `VITE_FIREBASE_...` değişkenleri kullanılarak Firebase yapılandırılacak ve başlatılacak.
    - Başlatılan Firebase uygulaması (`firebaseApp`) ve gerekli servisler (Firestore `db`, Auth `auth`, Storage `storage`) dışa aktarılacak (`export`).
    - Bu boot dosyası, Quasar uygulamasının başlangıcında çalışacak şekilde ayarlanacak.

2.  **Firebase Data Service Implementasyonunu Oluşturma:**

    - `src/core/data-layer/implementations/firebase` dizini oluşturulacak.
    - Bu dizin altında `firebaseDataService.ts` adında yeni bir dosya oluşturulacak.
    - Bu dosya, `IDataService<T>` arayüzünü implemente edecek. `T`, `IBaseModel`'den türeyen herhangi bir veri modelini temsil edecek.
    - Firebase Firestore veritabanı (`db`) kullanılacak.
    - `IDataService` arayüzündeki `getAll`, `getById`, `create`, `update`, `delete` metodları Firestore operasyonları ile implemente edilecek.
    - Firestore'dan gelen verilerin `IBaseModel` arayüzüne uygun hale getirilmesi için gerekli dönüştürme işlemleri yapılacak. Özellikle tarih alanları (`createdAt`, `updatedAt`) Firestore Timestamp'lerinden JavaScript `Date` objelerine dönüştürülecek.
    - Hata yönetimi (örneğin, kayıt bulunamadığında `undefined` dönme) sağlanacak.
    - Bu implementasyonun yalnızca `import.meta.env.VITE_DATA_SOURCE_TYPE === 'firebase'` koşulu sağlandığında kullanılması gerektiği dikkate alınacak.

3.  **Quasar Yapılandırmasını Güncelleme:**
    - `quasar.config.ts` dosyası güncellenecek.
    - `boot` dizinindeki ayarlara `firebase.ts` dosyası eklenecek, böylece uygulama başlangıcında Firebase başlatma scripti çalışacak.

## Dosya Yapısı (Plan Sonrası)

```
src/
├── boot/
│   ├── ... (diğer boot dosyaları)
│   └── firebase.ts         <-- Yeni dosya
├── core/
│   └── data-layer/
│       ├── implementations/
│       │   └── firebase/
│       │       └── firebaseDataService.ts  <-- Yeni dosya
│       ├── IBaseModel.ts
│       ├── IDataService.ts
│       ├── IReadService.ts
│       └── IWriteService.ts
└── ... (diğer dizinler ve dosyalar)
```

## Mimari Diyagram

```mermaid
graph TD
    A[Uygulama Başlangıcı] --> B(Quasar Boot Süreci)
    B --> C{VITE_DATA_SOURCE_TYPE === 'firebase'?}
    C -- Evet --> D(src/boot/firebase.ts Çalışır)
    D --> E(Firebase Uygulaması Başlatılır)
    E --> F(Firestore, Auth, Storage Servisleri Hazır)
    F --> G(Servisler Export Edilir)
    G --> H(src/core/data-layer/implementations/firebase/firebaseDataService.ts)
    H --> I(IDataService Implementasyonu Firestore Kullanır)
    I --> J(CRUD Operasyonları Firestore Üzerinden Yapılır)
    C -- Hayır --> K(Başka Data Source Implementasyonu Kullanılır)
```
