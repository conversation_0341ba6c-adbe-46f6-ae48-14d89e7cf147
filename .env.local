# Uygulama Adı
VITE_APP_NAME="Servis Ta<PERSON>p By AI"

# Uygulama Versiyonu
VITE_APP_VERSION="1.0.0"

# Veri Kaynağı Tipi (firebase, restapi, mock)
VITE_DATA_SOURCE_TYPE="firebase"

# REST API Ayarları (Eğer VITE_DATA_SOURCE_TYPE="restapi")
VITE_API_BASE_URL="http://localhost:8080/api"
VITE_API_TIMEOUT="30000"

# Firebase Ayarları (Eğer VITE_DATA_SOURCE_TYPE="firebase")
VITE_FIREBASE_API_KEY="AIzaSyA424oIU74ctZk4Iblmnfpx6HU6xkL2NJY"
VITE_FIREBASE_AUTH_DOMAIN="projecty-3cc80.firebaseapp.com"
VITE_FIREBASE_PROJECT_ID="projecty-3cc80"
VITE_FIREBASE_STORAGE_BUCKET="projecty-3cc80.appspot.com"
VITE_FIREBASE_MESSAGING_SENDER_ID="987304463490"
VITE_FIREBASE_APP_ID="1:987304463490:web:6c59cb3652d9292dacf89b"
VITE_FIREBASE_MEASUREMENT_ID=""

# i18n Ayarları
VITE_DEFAULT_LOCALE="tr-TR"
VITE_FALLBACK_LOCALE="en-US"

# Tema Ayarları (light, dark, auto)
VITE_DEFAULT_THEME="light"

# Loglama Ayarları (error, warn, info, debug, trace)
VITE_LOG_LEVEL="debug"

# Notifikasyon Ayarları (Quasar Notify)
# Varsayılan bildirim pozisyonu (Quasar Notify pozisyonları)
VITE_NOTIFICATION_DEFAULT_POSITION="bottom-right"
# Varsayılan bildirim zaman aşımı (milisaniye)
VITE_NOTIFICATION_DEFAULT_TIMEOUT="5000"
# Bildirimleri gruplama ("true" | "false")
VITE_NOTIFICATION_DEFAULT_GROUP="true"
# Varsayılan kapatma düğmesi etiketi (Boş = Quasar varsayılanı, veya ikon/metin)
VITE_NOTIFICATION_DEFAULT_CLOSE_BTN_LABEL=""