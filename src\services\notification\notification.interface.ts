// src/services/notification/notification.interface.ts
import type { QNotifyCreateOptions } from 'quasar';

export interface INotificationService {
  show(options: QNotifyCreateOptions): void;
  success(message: string, options?: QNotifyCreateOptions): void;
  error(message: string, options?: QNotifyCreateOptions): void;
  warning(message: string, options?: QNotifyCreateOptions): void;
  info(message: string, options?: QNotifyCreateOptions): void;
}
